const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const puppeteer = require('puppeteer');
const { spawn } = require('child_process');
const RuntimeErrorHandler = require('../build-time-repair/RuntimeErrorHandler');

/**
 * PageValidator - 页面运行时验证器
 *
 * 功能：
 * 1. 启动开发服务器
 * 2. 使用 Puppeteer 访问每个路由页面
 * 3. 捕获页面错误和控制台输出
 * 4. 集成 RuntimeErrorHandler 进行错误处理
 * 5. 生成验证报告
 */
class PageValidator {
  constructor(projectPath, routes, options = {}) {
    this.projectPath = projectPath;
    this.routes = routes || [];
    this.options = {
      port: 3000,
      timeout: 30000,
      headless: true,
      devCommand: 'npm run dev',
      baseUrl: null,
      verbose: false,
      autoFix: false,
      waitForServer: 10000,
      pageTimeout: 5000,
      ...options
    };

    this.baseUrl = this.options.baseUrl || `http://localhost:${this.options.port}`;
    this.devServer = null;
    this.browser = null;
    this.validationResults = [];
    this.errors = [];

    // 集成运行时错误处理器
    if (this.options.autoFix) {
      this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
        port: this.options.port,
        autoFix: true,
        verbose: this.options.verbose
      });
    }
  }

  /**
   * 验证所有页面
   */
  async validateAllPages() {
    console.log(chalk.blue(`🔍 开始验证 ${this.routes.length} 个页面...`));

    try {
      // 1. 启动开发服务器
      await this.startDevServer();

      // 2. 启动浏览器
      await this.startBrowser();

      // 3. 验证每个页面
      for (let i = 0; i < this.routes.length; i++) {
        const route = this.routes[i];
        console.log(chalk.gray(`   [${i + 1}/${this.routes.length}] 验证页面: ${route.path}`));

        const result = await this.validateSinglePage(route);
        this.validationResults.push(result);

        // 短暂延迟避免过快访问
        await this.sleep(500);
      }

      // 4. 生成报告
      const report = this.generateReport();

      console.log(chalk.green(`✅ 页面验证完成`));
      this.printSummary();

      return {
        success: true,
        results: this.validationResults,
        report: report,
        errors: this.errors
      };

    } catch (error) {
      console.error(chalk.red(`❌ 页面验证失败: ${error.message}`));
      return {
        success: false,
        results: this.validationResults,
        report: null,
        errors: [...this.errors, error.message]
      };
    } finally {
      await this.cleanup();
    }
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    if (this.options.baseUrl) {
      console.log(chalk.gray(`   使用外部服务器: ${this.baseUrl}`));
      return;
    }

    console.log(chalk.gray(`   启动开发服务器...`));

    return new Promise((resolve, reject) => {
      // 解析开发命令
      const [command, ...args] = this.options.devCommand.split(' ');

      this.devServer = spawn(command, args, {
        cwd: this.projectPath,
        stdio: 'pipe', // 总是使用 pipe 以便监听输出
        env: {
          ...process.env,
          PORT: this.options.port.toString()
        }
      });

      let serverReady = false;
      let output = '';
      let detectedPort = null;

      // 监听输出判断服务器是否启动
      if (this.devServer.stdout) {
        this.devServer.stdout.on('data', (data) => {
          const text = data.toString();
          output += text;

          if (this.options.verbose) {
            console.log(text);
          }

          // 添加更多调试信息
          // if (this.options.verbose && !serverReady) {
          //   console.log(chalk.cyan(`   🔍 检查文本: "${text.trim()}"`));
          //   console.log(chalk.cyan(`   🔍 包含 'Local:': ${text.includes('Local:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'localhost:': ${text.includes('localhost:')}`));
          //   console.log(chalk.cyan(`   🔍 包含 'App running at': ${text.includes('App running at')}`));
          // }

          // 检查服务器启动标志并提取端口
          if (!serverReady && (
            text.includes('Local:') ||
            text.includes('localhost:') ||
            text.includes('App running at') ||
            text.includes('Network:')
          )) {
            if (this.options.verbose) {
              console.log(chalk.blue(`   🔍 检测到服务器启动信息: ${text.trim()}`));
            }

            // 尝试提取端口号
            const portMatch = text.match(/localhost:(\d+)/);
            if (portMatch) {
              detectedPort = parseInt(portMatch[1]);
              if (this.options.verbose) {
                console.log(chalk.gray(`   检测到服务器端口: ${detectedPort}`));
              }

              // 简化逻辑：直接认为服务器已经准备好了
              const actualPort = detectedPort;
              this.options.port = actualPort; // 更新实际端口
              this.baseUrl = `http://localhost:${actualPort}`;

              if (this.options.verbose) {
                console.log(chalk.green(`   ✅ 服务器启动成功 (端口: ${actualPort})`));
              }

              serverReady = true;
              resolve();
            }
          }
        });
      }

      if (this.devServer.stderr) {
        this.devServer.stderr.on('data', (data) => {
          const text = data.toString();
          if (this.options.verbose) {
            console.error(chalk.red(text));
          }
        });
      }

      this.devServer.on('error', (error) => {
        reject(new Error(`启动开发服务器失败: ${error.message}`));
      });

      this.devServer.on('exit', (code) => {
        if (code !== 0 && !serverReady) {
          reject(new Error(`开发服务器异常退出，代码: ${code}`));
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!serverReady) {
          reject(new Error(`开发服务器启动超时 (${this.options.waitForServer}ms)`));
        }
      }, this.options.waitForServer);
    });
  }

  /**
   * 带重试的服务器验证
   */
  async verifyServerWithRetry(port, resolve, reject, attempt = 1, maxAttempts = 5) {
    const delay = attempt * 2000; // 递增延迟：2s, 4s, 6s, 8s, 10s

    setTimeout(async () => {
      try {
        const isReady = await this.verifyServerReady(port);

        if (isReady) {
          this.options.port = port; // 更新实际端口
          this.baseUrl = `http://localhost:${port}`;
          if (this.options.verbose) {
            console.log(chalk.green(`   ✅ 服务器验证成功 (端口: ${port}, 尝试: ${attempt}/${maxAttempts})`));
          }
          resolve();
        } else if (attempt < maxAttempts) {
          if (this.options.verbose) {
            console.log(chalk.yellow(`   ⚠️  端口 ${port} 验证失败，${delay/1000}秒后重试 (${attempt}/${maxAttempts})...`));
          }
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        } else {
          if (this.options.verbose) {
            console.log(chalk.red(`   ❌ 服务器验证失败，已达到最大重试次数 (${maxAttempts})`));
          }
          // 最后一次尝试失败，但不要 reject，让超时处理
        }
      } catch (error) {
        if (this.options.verbose) {
          console.log(chalk.yellow(`   ⚠️  服务器验证出错 (尝试 ${attempt}/${maxAttempts}): ${error.message}`));
        }
        if (attempt < maxAttempts) {
          this.verifyServerWithRetry(port, resolve, reject, attempt + 1, maxAttempts);
        }
      }
    }, delay);
  }

  /**
   * 验证服务器是否真的可以访问
   */
  async verifyServerReady(port) {
    try {
      const axios = require('axios');
      const response = await axios.get(`http://localhost:${port}`, {
        timeout: 5000,
        validateStatus: () => true // 接受任何状态码
      });

      return response.status < 500; // 只要不是服务器错误就认为可用
    } catch (error) {
      return false;
    }
  }

  /**
   * 启动浏览器
   */
  async startBrowser() {
    console.log(chalk.gray(`   启动浏览器...`));

    this.browser = await puppeteer.launch({
      headless: this.options.headless,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
  }

  /**
   * 验证单个页面
   */
  async validateSinglePage(route) {
    const url = this.baseUrl + route.path;
    const result = {
      route: route,
      url: url,
      success: false,
      errors: [],
      warnings: [],
      consoleMessages: [],
      networkErrors: [],
      loadTime: 0,
      timestamp: new Date().toISOString()
    };

    try {
      const page = await this.browser.newPage();
      const startTime = Date.now();

      // 设置页面事件监听
      this.setupPageListeners(page, result);

      // 设置超时
      page.setDefaultTimeout(this.options.pageTimeout);
      page.setDefaultNavigationTimeout(this.options.pageTimeout);

      // 访问页面
      const response = await page.goto(url, {
        waitUntil: 'networkidle0',
        timeout: this.options.pageTimeout
      });

      result.loadTime = Date.now() - startTime;

      // 检查响应状态
      if (!response.ok()) {
        result.errors.push(`HTTP ${response.status()}: ${response.statusText()}`);
      }

      // 等待页面渲染
      await page.waitForTimeout(1000);

      // 检查页面是否包含 Vue 应用
      const hasVueApp = await page.evaluate(() => {
        return !!(window.Vue || document.querySelector('[data-v-]') || document.querySelector('#app'));
      });

      if (!hasVueApp) {
        result.warnings.push('页面可能未正确加载 Vue 应用');
      }

      // 检查是否有 JavaScript 错误
      const jsErrors = await page.evaluate(() => {
        return window.__pageErrors || [];
      });

      if (jsErrors.length > 0) {
        result.errors.push(...jsErrors);
      }

      // 如果没有错误，标记为成功
      if (result.errors.length === 0) {
        result.success = true;
      }

      await page.close();

    } catch (error) {
      result.errors.push(`页面访问失败: ${error.message}`);

      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  ${route.path}: ${error.message}`));
      }
    }

    return result;
  }

  /**
   * 设置页面事件监听器
   */
  setupPageListeners(page, result) {
    // 监听控制台消息
    page.on('console', (msg) => {
      const message = {
        type: msg.type(),
        text: msg.text(),
        timestamp: new Date().toISOString()
      };

      result.consoleMessages.push(message);

      // 将错误和警告添加到结果中
      if (msg.type() === 'error') {
        result.errors.push(`Console Error: ${msg.text()}`);
      } else if (msg.type() === 'warning') {
        result.warnings.push(`Console Warning: ${msg.text()}`);
      }
    });

    // 监听页面错误
    page.on('pageerror', (error) => {
      result.errors.push(`Page Error: ${error.message}`);
    });

    // 监听请求失败
    page.on('requestfailed', (request) => {
      const networkError = {
        url: request.url(),
        method: request.method(),
        failure: request.failure()?.errorText || 'Unknown error'
      };

      result.networkErrors.push(networkError);

      // 只有关键资源失败才算错误
      if (request.resourceType() === 'document' || request.resourceType() === 'script') {
        result.errors.push(`Network Error: ${request.url()} - ${networkError.failure}`);
      }
    });

    // 注入错误收集脚本
    page.evaluateOnNewDocument(() => {
      window.__pageErrors = [];

      // 捕获全局错误
      window.addEventListener('error', (event) => {
        window.__pageErrors.push({
          message: event.message,
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        });
      });

      // 捕获 Promise 错误
      window.addEventListener('unhandledrejection', (event) => {
        window.__pageErrors.push({
          message: `Unhandled Promise Rejection: ${event.reason}`,
          type: 'promise'
        });
      });
    });
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    const totalPages = this.validationResults.length;
    const successfulPages = this.validationResults.filter(r => r.success).length;
    const failedPages = this.validationResults.filter(r => !r.success);

    const report = {
      summary: {
        total: totalPages,
        successful: successfulPages,
        failed: failedPages.length,
        successRate: totalPages > 0 ? (successfulPages / totalPages * 100).toFixed(2) : 0
      },
      results: this.validationResults,
      failedPages: failedPages,
      timestamp: new Date().toISOString()
    };

    return report;
  }

  /**
   * 打印验证摘要
   */
  printSummary() {
    const report = this.generateReport();

    console.log(chalk.blue('\n📊 验证结果摘要:'));
    console.log(chalk.gray(`   总页面数: ${report.summary.total}`));
    console.log(chalk.green(`   成功: ${report.summary.successful}`));
    console.log(chalk.red(`   失败: ${report.summary.failed}`));
    console.log(chalk.blue(`   成功率: ${report.summary.successRate}%`));

    if (report.failedPages.length > 0) {
      console.log(chalk.red('\n❌ 失败的页面:'));
      for (const failed of report.failedPages) {
        console.log(chalk.red(`   ${failed.route.path}: ${failed.errors[0] || '未知错误'}`));
      }
    }
  }

  /**
   * 清理资源
   */
  async cleanup() {
    try {
      if (this.browser) {
        await this.browser.close();
      }

      if (this.devServer && !this.devServer.killed) {
        this.devServer.kill('SIGTERM');

        // 等待进程结束
        await new Promise((resolve) => {
          this.devServer.on('exit', resolve);
          setTimeout(resolve, 5000); // 5秒超时
        });
      }

      if (this.runtimeErrorHandler) {
        // 清理运行时错误处理器
      }
    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`⚠️  清理资源时出错: ${error.message}`));
      }
    }
  }

  /**
   * 工具方法：延迟
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取验证结果
   */
  getResults() {
    return this.validationResults;
  }

  /**
   * 保存报告到文件
   */
  async saveReport(outputPath) {
    const report = this.generateReport();

    // 生成 Markdown 报告
    const markdown = this.generateMarkdownReport(report);

    await fs.writeFile(outputPath, markdown, 'utf8');
    console.log(chalk.green(`📄 验证报告已保存: ${outputPath}`));
  }

  /**
   * 生成 Markdown 格式的报告
   */
  generateMarkdownReport(report) {
    let markdown = `# 页面验证报告\n\n`;
    markdown += `生成时间: ${report.timestamp}\n\n`;

    markdown += `## 摘要\n\n`;
    markdown += `- 总页面数: ${report.summary.total}\n`;
    markdown += `- 成功: ${report.summary.successful}\n`;
    markdown += `- 失败: ${report.summary.failed}\n`;
    markdown += `- 成功率: ${report.summary.successRate}%\n\n`;

    if (report.failedPages.length > 0) {
      markdown += `## 失败的页面\n\n`;
      for (const failed of report.failedPages) {
        markdown += `### ${failed.route.path}\n\n`;
        markdown += `- URL: ${failed.url}\n`;
        markdown += `- 加载时间: ${failed.loadTime}ms\n`;

        if (failed.errors.length > 0) {
          markdown += `- 错误:\n`;
          for (const error of failed.errors) {
            markdown += `  - ${error}\n`;
          }
        }

        if (failed.warnings.length > 0) {
          markdown += `- 警告:\n`;
          for (const warning of failed.warnings) {
            markdown += `  - ${warning}\n`;
          }
        }

        markdown += `\n`;
      }
    }

    markdown += `## 详细结果\n\n`;
    for (const result of report.results) {
      const status = result.success ? '✅' : '❌';
      markdown += `- ${status} ${result.route.path} (${result.loadTime}ms)\n`;
    }

    return markdown;
  }
}

module.exports = PageValidator;
